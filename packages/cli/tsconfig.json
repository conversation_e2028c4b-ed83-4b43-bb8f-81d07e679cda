{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2020"], "types": ["node", "vitest/globals"]}, "include": ["index.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.json", "/**/*.ts", "./package.json"], "exclude": ["node_modules", "dist", "src/**/*.test.ts", "src/**/*.test.tsx", "src/test-utils"], "references": [{"path": "../core"}]}