import { ElcAgent } from './bundle/api.js';

const agent = new ElcAgent({
  // 模型：htsc::saas-deepseek-v3
  // model: 'saas-doubao-15-pro-32k',
  model: 'saas-deepseek-v3',
  apiKey: 'custom-llm-api-key',
  endpoint: 'http://proxyllm.sit.saas.htsc/v1',
  extension: {
    mcpServers: {
      chart: {
        command: 'npx',
        args: ['-y', '@antv/mcp-server-chart'],
        trust: false
      }
    },
    excludeTools: ['run_shell_command']
  },
  log: false,
});

const result = await agent.run('Please generate a bar chart for sales data, and use mock data');

console.log('ElcAgent', result);
